<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="960px"
    :before-close="handleClose"
    class="add-unit-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <el-form ref="addUnitForm" :model="formData" :rules="formRules">
        <div class="form-row">
          <el-form-item :label="$T('机组名称')" prop="unitName">
            <el-input
              v-model="formData.unitName"
              :placeholder="$T('请输入内容')"
              class="form-input"
            />
          </el-form-item>
          <el-form-item :label="$T('机组类型')" prop="unitType">
            <el-select
              v-model="formData.unitType"
              class="form-select"
              :placeholder="$T('请选择机组类型')"
              :disabled="mode === 'edit'"
            >
              <el-option
                v-for="option in unitTypeOptions"
                :key="option.value"
                :label="$T(option.label)"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>

      <!-- 资源列表区域 -->
      <div v-if="formData.unitType" class="resource-section">
        <div class="section-title">{{ resourceListTitle }}</div>

        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <el-input
            v-model="resourceSearch"
            :placeholder="$T('请输入关键字')"
            prefix-icon="el-icon-search"
            class="search-input"
            @change="handleSearchChange"
          />
          <CustomElSelect
            v-model="selectedArea"
            :prefix_in="$T('区域')"
            class="district-select"
          >
            <el-option
              v-for="option in areaOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </CustomElSelect>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <el-table
            ref="resourceTable"
            :data="tableData"
            @selection-change="handleSelectionChange"
            max-height="440"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              prop="index"
              :label="$T('序号')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ String(scope.row.index).padStart(2, "0") }}
              </template>
            </el-table-column>
            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="180"
            />
            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="150"
            />
            <el-table-column
              prop="district"
              :label="$T('区域')"
              min-width="100"
            />
            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="140"
            />
            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T("是") : $T("否") }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="resource-pagination">
          <span>
            {{ $T("共") }}
            <span class="count-number">{{ totalResourceCount }}</span>
            {{ $T("个") }}
          </span>
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalResourceCount"
            layout="sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getAvailableResources,
  getDistricts
} from "@/api/resource-aggregation";
import { UNIT_TYPE_OPTIONS, UNIT_TYPE_MESSAGES } from "@/utils/unitTypeEnum";

export default {
  name: "AddUnitDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: "add",
      validator: value => ["add", "edit"].includes(value)
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,

      // 表单数据
      formData: {
        unitName: "",
        unitType: 1
      },

      // 表单验证规则
      formRules: {
        unitName: [
          {
            required: true,
            message: this.$T("请输入机组名称"),
            trigger: "blur"
          },
          {
            min: 1,
            max: 50,
            message: this.$T("长度在 1 到 50 个字符"),
            trigger: "blur"
          }
        ],
        unitType: [
          {
            required: true,
            message: this.$T("请选择机组类型"),
            trigger: "change"
          }
        ]
      },

      // 资源搜索和筛选
      resourceSearch: "",
      selectedArea: "",
      areaOptions: [],

      // 资源列表数据
      tableData: [],
      selectedResources: [],

      // 分页
      currentPage: 1,
      pageSize: 10,
      // 总数
      totalResourceCount: 0
    };
  },
  computed: {
    // 机组类型选项
    unitTypeOptions() {
      return UNIT_TYPE_OPTIONS.filter(option => option.value !== 0);
    },

    // 弹窗标题
    dialogTitle() {
      return this.mode === "edit" ? this.$T("编辑") : this.$T("新增");
    },

    // 资源列表标题
    resourceListTitle() {
      if (this.formData.unitType) {
        const selectedOption = this.unitTypeOptions.find(
          option => option.value === this.formData.unitType
        );
        const listType = this.$T("资源列表");
        return `${selectedOption.label}${listType}`;
      }
      return "";
    }
  },
  watch: {
    visible: {
      async handler(val) {
        this.dialogVisible = val;
        if (val) {
          await this.initDialog();
        }
      }
    },

    dialogVisible(val) {
      this.$emit("update:visible", val);
    },

    // 监听区域筛选变化
    selectedArea() {
      this.currentPage = 1; // 重置到第一页
      this.loadAvailableResources();
    },

    "formData.unitType": {
      handler(newType, oldType) {
        if (newType && newType !== oldType) {
          if (this.mode === "add") {
            this.showUnitTypeMessage(newType);
          }
          this.loadAvailableResources();
        }
      }
    }
  },
  mounted() {
    this.loadDistricts();
  },
  methods: {
    // 初始化弹窗
    async initDialog() {
      if (this.mode === "edit" && this.editData) {
        // 编辑模式：使用传入的数据
        this.formData = {
          unitName: this.editData.unitName || "",
          unitType: this.editData.typeValue || 1
        };
      } else {
        // 新增模式：使用空数据
        this.formData = {
          unitName: "",
          unitType: ""
        };
        this.selectedResources = [];
      }

      this.resourceSearch = "";
      // 如果有区域选项，设置为第一个，否则为空
      if (this.areaOptions.length > 0) {
        this.selectedArea = this.areaOptions[0].value;
      } else {
        this.selectedArea = "";
      }
      this.currentPage = 1;

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.addUnitForm) {
          this.$refs.addUnitForm.clearValidate();
        }
      });
    },
    // 显示机组类型提示信息
    showUnitTypeMessage(unitType) {
      const message = UNIT_TYPE_MESSAGES[unitType];

      if (message) {
        this.$message({
          message: this.$T(message),
          type: "info",
          showClose: true
        });
      }
    },

    // 加载区域列表
    async loadDistricts() {
      try {
        const response = await getDistricts();
        if (response && response.data) {
          this.areaOptions = response.data.map(item => ({
            label: item.districtName,
            value: item.district
          }));

          // 设置默认选中第一个区域
          if (this.areaOptions.length > 0) {
            this.selectedArea = this.areaOptions[0].value;
          }
        }
      } catch (error) {
        this.$message.error(this.$T("加载区域列表失败"));
      }
    },

    // 加载可用资源列表
    async loadAvailableResources() {
      if (!this.formData.unitType) {
        this.allResourceData = [];
        return;
      }

      try {
        const params = {
          unitType: this.formData.unitType,
          district: this.selectedArea,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加搜索关键词参数
        if (this.resourceSearch.trim()) {
          params.searchKeyword = this.resourceSearch.trim();
        }

        // 编辑模式下传入机组ID
        if (this.mode === "edit" && this.editData && this.editData.unitId) {
          params.unitId = this.editData.unitId;
        }

        const response = await getAvailableResources(params);
        if (response && response.data) {
          const resourceList = response.data.records || [];
          this.tableData = resourceList.map((item, index) => ({
            index: (this.currentPage - 1) * this.pageSize + index + 1,
            resourceId: item.id,
            resourceName: item.resource_name,
            district: item.district,
            capacity: item.registered_capacity,
            directControl: item.platform_direct_control
          }));

          // 如果API返回了total，使用API的total，否则使用list的长度
          this.totalResourceCount =
            response.data.total !== undefined
              ? response.data.total
              : response.data.records.length;
        } else {
          this.tableData = [];
        }
      } catch (error) {
        this.$message.error(this.$T("加载可用资源失败"));
        this.allResourceData = [];
      }
    },
    // 表格选中状态变化
    handleSelectionChange(selection) {
      this.selectedResources = selection;
    },

    // 资源分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadAvailableResources();
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.loadAvailableResources();
    },

    // 处理搜索关键字变化
    handleSearchChange() {
      this.currentPage = 1; // 重置到第一页
      this.loadAvailableResources(); // 重新加载资源列表
    },

    // 关闭弹窗
    handleClose() {
      this.resetDialogState();
      this.dialogVisible = false;
    },

    // 取消
    handleCancel() {
      this.resetDialogState();
      this.dialogVisible = false;
    },

    // 重置对话框状态
    resetDialogState() {
      this.formData = {
        unitName: "",
        unitType: ""
      };
      this.selectedResources = [];
      this.resourceSearch = "";
      this.currentPage = 1;
    },

    // 确定
    handleConfirm() {
      // 表单验证
      this.$refs.addUnitForm.validate(valid => {
        if (valid) {
          // 提交数据
          const submitData = {
            ...this.formData,
            selectedResources: this.selectedResources
          };

          if (this.mode === "edit") {
            this.$emit("update", submitData);
            this.$message.success(this.$T("编辑成功"));
          } else {
            this.$emit("confirm", submitData);
            this.$message.success(this.$T("新增成功"));
          }

          this.dialogVisible = false;
        } else {
          this.$message.warning(this.$T("请完善表单信息"));
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.add-unit-dialog {
  // 弹窗整体居中
  :deep(.el-dialog) {
    margin-top: 5vh !important;
  }
  .dialog-content {
    @include padding(J4);

    .form-row {
      display: flex;
      gap: var(--J4);
    }

    .resource-section {
      .section-title {
        @include margin_bottom(J2);
      }

      .resource-filter {
        display: flex;
        align-items: center;
        @include margin_bottom(J3);
        gap: var(--J3);
        .search-input {
          width: 240px;
        }
        .district-select {
          width: 240px;
        }
      }
      .resource-table {
        @include margin_bottom(J3);
      }

      .resource-pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: var(--J3);
        .count-number {
          @include font_color(ZS);
        }
        .page-size-select {
          width: 100px;
        }
      }
    }
  }
}
</style>
